# Example environment configuration for Crypto Portfolio Analyzer
# Copy this file to .env and update with your actual values

# Application settings
CRYPTO_PORTFOLIO_APP_DEBUG=false
CRYPTO_PORTFOLIO_APP_VERBOSE=false

# Logging configuration
CRYPTO_PORTFOLIO_LOGGING_LEVEL=INFO
CRYPTO_PORTFOLIO_LOGGING_STRUCTURED=true

# Plugin settings
CRYPTO_PORTFOLIO_PLUGINS_HOT_RELOAD=true
CRYPTO_PORTFOLIO_PLUGINS_AUTO_DISCOVER=true

# API configuration
CRYPTO_PORTFOLIO_API_TIMEOUT=30
CRYPTO_PORTFOLIO_API_RETRIES=3

# Cache settings
CRYPTO_PORTFOLIO_CACHE_ENABLED=true
CRYPTO_PORTFOLIO_CACHE_TTL=300
CRYPTO_PORTFOLIO_CACHE_BACKEND=memory

# Database configuration
CRYPTO_PORTFOLIO_DATABASE_URL=sqlite:///crypto_portfolio.db
CRYPTO_PORTFOLIO_DATABASE_ECHO=false

# Security settings
CRYPTO_PORTFOLIO_SECURITY_KMS_ENABLED=false
CRYPTO_PORTFOLIO_SECURITY_KMS_KEY_ID=
CRYPTO_PORTFOLIO_SECURITY_KMS_REGION=us-east-1

# External API keys (store in secrets for production)
CRYPTO_PORTFOLIO_SERVICES_COINGECKO_API_KEY=
CRYPTO_PORTFOLIO_SERVICES_COINMARKETCAP_API_KEY=
CRYPTO_PORTFOLIO_SERVICES_BINANCE_API_KEY=
CRYPTO_PORTFOLIO_SERVICES_BINANCE_SECRET_KEY=

# Sentry configuration (optional)
CRYPTO_PORTFOLIO_LOGGING_SENTRY_ENABLED=false
CRYPTO_PORTFOLIO_LOGGING_SENTRY_DSN=
CRYPTO_PORTFOLIO_LOGGING_SENTRY_ENVIRONMENT=development

# Development settings
CRYPTO_PORTFOLIO_DEVELOPMENT_MOCK_API=false
CRYPTO_PORTFOLIO_DEVELOPMENT_TEST_DATA=false
CRYPTO_PORTFOLIO_DEVELOPMENT_PROFILING=false
