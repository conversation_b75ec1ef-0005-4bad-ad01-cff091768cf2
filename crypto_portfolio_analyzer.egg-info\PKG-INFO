Metadata-Version: 2.4
Name: crypto-portfolio-analyzer
Version: 0.1.0
Summary: A Python CLI tool for tracking cryptocurrency portfolios with real-time prices and analytics
Author-email: Crypto Portfolio Analyzer Team <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer
Project-URL: Documentation, https://crypto-portfolio-analyzer.readthedocs.io
Project-URL: Repository, https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer
Project-URL: Bug Tracker, https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer/issues
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Financial and Insurance Industry
Classifier: License :: OSI Approved :: MIT License
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Topic :: Office/Business :: Financial :: Investment
Requires-Python: >=3.8
Description-Content-Type: text/markdown
License-File: LICENSE_HEADER.txt
Requires-Dist: click>=8.0.0
Requires-Dist: requests>=2.28.0
Requires-Dist: pandas>=1.5.0
Requires-Dist: matplotlib>=3.6.0
Requires-Dist: tabulate>=0.9.0
Requires-Dist: reportlab>=3.6.0
Requires-Dist: watchdog>=2.1.0
Requires-Dist: pyyaml>=6.0
Requires-Dist: python-dotenv>=0.19.0
Requires-Dist: cryptography>=3.4.0
Requires-Dist: sentry-sdk>=1.9.0
Requires-Dist: boto3>=1.26.0
Requires-Dist: aiofiles>=0.8.0
Requires-Dist: rich>=12.0.0
Requires-Dist: ipython>=8.0.0
Provides-Extra: dev
Requires-Dist: pytest>=7.0.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.20.0; extra == "dev"
Requires-Dist: pytest-cov>=4.0.0; extra == "dev"
Requires-Dist: mypy>=1.0.0; extra == "dev"
Requires-Dist: flake8>=5.0.0; extra == "dev"
Requires-Dist: bandit>=1.7.0; extra == "dev"
Requires-Dist: pre-commit>=2.20.0; extra == "dev"
Requires-Dist: black>=22.0.0; extra == "dev"
Requires-Dist: isort>=5.10.0; extra == "dev"
Requires-Dist: tox>=4.0.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.0.0; extra == "test"
Requires-Dist: pytest-asyncio>=0.20.0; extra == "test"
Requires-Dist: pytest-cov>=4.0.0; extra == "test"
Requires-Dist: pytest-mock>=3.8.0; extra == "test"
Requires-Dist: responses>=0.22.0; extra == "test"
Provides-Extra: docs
Requires-Dist: mkdocs>=1.4.0; extra == "docs"
Requires-Dist: mkdocs-material>=8.5.0; extra == "docs"
Requires-Dist: mkdocstrings[python]>=0.19.0; extra == "docs"
Dynamic: license-file

# Crypto Portfolio Analyzer

A sophisticated Python CLI tool for cryptocurrency portfolio management with real-time price fetching, analytics, and comprehensive reporting capabilities.

[![CI/CD Pipeline](https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer/actions)
[![codecov](https://codecov.io/gh/crypto-portfolio-analyzer/crypto-portfolio-analyzer/branch/main/graph/badge.svg)](https://codecov.io/gh/crypto-portfolio-analyzer/crypto-portfolio-analyzer)
[![PyPI version](https://badge.fury.io/py/crypto-portfolio-analyzer.svg)](https://badge.fury.io/py/crypto-portfolio-analyzer)
[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## Features

### 🚀 Feature 1: Meta-Driven CLI & Configuration (Implemented)

- **Plugin-Based Architecture**: Hot-reloadable plugins with automatic discovery
- **Hierarchical Commands**: Context inheritance between parent and child commands
- **Advanced Configuration**: YAML defaults, environment overrides, encrypted secrets
- **Structured Logging**: JSON logging with dynamic sampling and Sentry integration
- **Code Quality**: Pre-commit hooks with mypy, flake8, and security scanning
- **Interactive Development**: Hidden debug REPL with IPython integration

### 🔮 Upcoming Features

- **Feature 2**: High-throughput price fetching with resilient API handling
- **Feature 3**: Domain-driven portfolio management with event sourcing
- **Feature 4**: Advanced analytics with streaming metrics and plugins
- **Feature 5**: Robust persistence with migrations and encryption
- **Feature 6**: Dynamic visualization and reporting engine
- **Feature 7**: Enterprise-grade export and distribution
- **Feature 8**: Complete CI/CD pipeline with observability

## Quick Start

### Installation

```bash
# Install from PyPI (when available)
pip install crypto-portfolio-analyzer

# Or install from source
git clone https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer.git
cd crypto-portfolio-analyzer
pip install -e .[dev]
```

### Basic Usage

```bash
# Show help
crypto-portfolio --help

# Check version
crypto-portfolio version

# List loaded plugins
crypto-portfolio plugins

# Portfolio management
crypto-portfolio portfolio status
crypto-portfolio portfolio add btc 0.5 --price 30000
crypto-portfolio portfolio list --format table

# Configuration management
crypto-portfolio config show
crypto-portfolio config set logging.level DEBUG
crypto-portfolio config secrets --list
```

### Development Mode

```bash
# Enable debug mode with verbose output
crypto-portfolio --debug --verbose portfolio status

# Access debug REPL (requires --debug flag)
crypto-portfolio --debug debug-repl
```

## Development Setup

### Prerequisites

- Python 3.8 or higher
- Git
- Docker (optional, for containerized development)

### Local Development Environment

1. **Clone the repository**:
   ```bash
   git clone https://github.com/crypto-portfolio-analyzer/crypto-portfolio-analyzer.git
   cd crypto-portfolio-analyzer
   ```

2. **Set up virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install development dependencies**:
   ```bash
   pip install -e .[dev]
   ```

4. **Set up pre-commit hooks**:
   ```bash
   pre-commit install
   ```

5. **Copy environment configuration**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

### Local KMS Emulator Setup

For development and testing of encrypted secrets without AWS KMS:

1. **Install LocalStack** (for KMS emulation):
   ```bash
   pip install localstack
   ```

2. **Start LocalStack**:
   ```bash
   localstack start -d
   ```

3. **Configure environment**:
   ```bash
   export CRYPTO_PORTFOLIO_SECURITY_KMS_ENABLED=false
   # The application will automatically use local encryption
   ```

### Test Secrets Injection

For development and testing:

1. **Set test secrets**:
   ```bash
   crypto-portfolio config secrets --set api_key "test_api_key_12345"
   crypto-portfolio config secrets --set secret_key "test_secret_key_67890"
   ```

2. **Verify secrets**:
   ```bash
   crypto-portfolio config secrets --list
   crypto-portfolio config secrets --get api_key
   ```

3. **Test encryption**:
   ```bash
   # Secrets are automatically encrypted and stored in crypto_portfolio_analyzer/config/secrets.enc
   ls -la crypto_portfolio_analyzer/config/
   ```

### Dynamic Plugin Loading

#### Creating Custom Plugins

1. **Create a plugin file** in the `plugins/` directory:
   ```python
   # plugins/my_custom_plugin.py
   from crypto_portfolio_analyzer.core.plugin_manager import BasePlugin
   
   class MyCustomPlugin(BasePlugin):
       """Custom plugin example."""
       
       __version__ = "1.0.0"
       __author__ = "Your Name"
       
       def __init__(self, name="my_custom_plugin"):
           super().__init__(name)
       
       async def initialize(self):
           print(f"Initializing {self.name}")
       
       async def teardown(self):
           print(f"Shutting down {self.name}")
       
       async def on_command_start(self, command_name, context):
           if command_name.startswith('portfolio'):
               print(f"Portfolio command started: {command_name}")
   ```

2. **Test hot-reloading**:
   ```bash
   # Start the application with hot-reload enabled
   crypto-portfolio --debug plugins
   
   # In another terminal, modify the plugin file
   # The plugin will be automatically reloaded
   ```

3. **Plugin development workflow**:
   ```bash
   # Watch plugin loading
   crypto-portfolio --debug --verbose plugins
   
   # Test plugin functionality
   crypto-portfolio portfolio status  # Your plugin events will fire
   
   # Debug plugin issues
   crypto-portfolio --debug debug-repl
   # In REPL: plugin_manager.get_plugin('my_custom_plugin')
   ```

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=crypto_portfolio_analyzer --cov-report=html

# Run specific test categories
pytest -m unit          # Unit tests only
pytest -m integration   # Integration tests only
pytest -m "not slow"    # Skip slow tests

# Run tests with verbose output
pytest -v --tb=short
```

### Test Categories

- **Unit Tests**: Fast, isolated component tests
- **Integration Tests**: Multi-component interaction tests
- **Async Tests**: Asynchronous functionality tests
- **Plugin Tests**: Plugin system and lifecycle tests

### Test Coverage

The project maintains >80% test coverage. View coverage reports:

```bash
# Generate HTML coverage report
pytest --cov=crypto_portfolio_analyzer --cov-report=html
open htmlcov/index.html
```

## Configuration

### Configuration Hierarchy

1. **Default Configuration**: `crypto_portfolio_analyzer/config/default.yaml`
2. **Environment-Specific**: `crypto_portfolio_analyzer/config/{environment}.yaml`
3. **Environment Variables**: `CRYPTO_PORTFOLIO_*` prefixed variables
4. **Encrypted Secrets**: `crypto_portfolio_analyzer/config/secrets.enc`

### Environment Variables

```bash
# Application settings
CRYPTO_PORTFOLIO_APP_DEBUG=false
CRYPTO_PORTFOLIO_APP_VERBOSE=false

# Logging configuration
CRYPTO_PORTFOLIO_LOGGING_LEVEL=INFO
CRYPTO_PORTFOLIO_LOGGING_STRUCTURED=true

# Plugin settings
CRYPTO_PORTFOLIO_PLUGINS_HOT_RELOAD=true

# Security settings
CRYPTO_PORTFOLIO_SECURITY_KMS_ENABLED=false
CRYPTO_PORTFOLIO_SECURITY_KMS_KEY_ID=your-kms-key-id
```

### Secret Management

```bash
# Set secrets (encrypted automatically)
crypto-portfolio config secrets --set api_key "your-api-key"
crypto-portfolio config secrets --set database_password "secure-password"

# List secret keys (values are hidden)
crypto-portfolio config secrets --list

# Get specific secret
crypto-portfolio config secrets --get api_key

# Delete secret
crypto-portfolio config secrets --delete old_api_key
```

## Architecture

### Core Components

- **CLI System**: Click-based hierarchical commands with context inheritance
- **Plugin Manager**: Hot-reloadable plugin system with lifecycle management
- **Event Bus**: Async publish-subscribe system for component communication
- **Configuration Manager**: Multi-tier configuration with encryption support
- **Logging System**: Structured JSON logging with sampling and Sentry integration

### Plugin Architecture

```
plugins/
├── __init__.py
├── portfolio.py      # Core portfolio management
├── config.py         # Configuration management
└── custom/           # Custom user plugins
    ├── my_plugin.py
    └── analytics.py
```

### Event System

```python
# Subscribe to events
event_bus.subscribe(EventType.COMMAND_START, my_handler)

# Publish events
await event_bus.publish_event(
    EventType.PLUGIN_LOADED,
    "plugin_manager",
    {"plugin_name": "my_plugin"}
)
```

## Contributing

### Development Workflow

1. **Fork and clone** the repository
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes** and add tests
4. **Run quality checks**: `pre-commit run --all-files`
5. **Run tests**: `pytest`
6. **Commit changes**: `git commit -m "Add amazing feature"`
7. **Push to branch**: `git push origin feature/amazing-feature`
8. **Create Pull Request**

### Code Quality Standards

- **Type Hints**: All code must include type hints
- **Documentation**: Docstrings for all public APIs
- **Testing**: >80% test coverage required
- **Linting**: Code must pass flake8, mypy, and bandit checks
- **Formatting**: Code formatted with Black and isort

### Pre-commit Hooks

The project uses comprehensive pre-commit hooks:

```bash
# Install hooks
pre-commit install

# Run manually
pre-commit run --all-files

# Update hooks
pre-commit autoupdate
```

## Troubleshooting

### Common Issues

1. **Plugin Loading Errors**:
   ```bash
   # Check plugin syntax
   python -m py_compile plugins/my_plugin.py
   
   # Debug plugin loading
   crypto-portfolio --debug plugins
   ```

2. **Configuration Issues**:
   ```bash
   # Validate configuration
   crypto-portfolio config validate
   
   # Check configuration sources
   crypto-portfolio config show --format yaml
   ```

3. **Secret Management**:
   ```bash
   # Reset secrets (creates new encryption key)
   rm crypto_portfolio_analyzer/config/secrets.enc
   rm crypto_portfolio_analyzer/config/.secret_key
   ```

### Debug Mode

```bash
# Enable comprehensive debugging
crypto-portfolio --debug --verbose command

# Access debug REPL
crypto-portfolio --debug debug-repl
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- Built with [Click](https://click.palletsprojects.com/) for CLI framework
- Uses [Rich](https://rich.readthedocs.io/) for beautiful terminal output
- Powered by [asyncio](https://docs.python.org/3/library/asyncio.html) for async operations
- Secured with [cryptography](https://cryptography.io/) for encryption

---

**Note**: This is Feature 1 of an 8-feature roadmap. Additional features including price fetching, analytics, visualization, and enterprise features are planned for future releases.
