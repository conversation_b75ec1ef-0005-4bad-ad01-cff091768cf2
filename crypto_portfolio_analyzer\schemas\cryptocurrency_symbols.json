{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Cryptocurrency Symbols", "description": "Valid cryptocurrency symbols for the portfolio analyzer", "type": "string", "enum": ["btc", "eth", "ada", "dot", "link", "ltc", "xrp", "bch", "eos", "xlm", "trx", "etc", "dash", "zec", "xmr", "neo", "qtum", "omg", "zrx", "bat", "knc", "rep", "storj", "gnt", "ant", "snt", "mana", "fun", "dnt", "cvc", "rlc", "pay", "req", "salt", "eng", "ast", "poe", "tnt", "rcn", "vet", "wan", "powr", "wings", "mth", "yoyow", "cdt", "tnb", "snm", "vib", "mco", "ela", "ppt", "fuel", "mana", "sand", "enj", "chz", "hot", "one", "matic", "atom", "algo", "xtz", "comp", "mkr", "aave", "snx", "uni", "sushi", "1inch", "crv", "yfi", "bal", "ren", "lrc", "band", "kava", "rsr", "ocean", "fet", "ctsi", "skl", "nu", "keep", "nkn", "grt", "api3", "badger", "farm", "alpha", "beta", "rari", "tribe", "fei", "mpl", "pool", "dpi", "index", "bed", "mvi", "data", "defi5", "cc10", "sol", "avax", "luna", "near", "ftm", "hbar", "egld", "theta", "tfuel", "klay", "celo", "ar", "fil", "icp", "flow", "mina", "rose", "oxt", "mask", "lpt", "axs", "slp", "sand", "mana", "enj", "chz", "alice", "tlm", "chr", "dydx", "ens", "imx", "ldo", "apt", "op", "arb", "blur", "pepe", "sui", "sei", "tia", "jup", "wif", "bonk", "floki", "shib", "doge"]}